2025-07-12 05:14:55.495 [info] Other tunnel running, attaching...
2025-07-12 05:14:55.495 [info] Running tunnel CLI

2025-07-12 05:14:55.496 [info] tunnel Spawning: c:\Users\<USER>\vscode-extion\.vscode-test\vscode-win32-x64-archive-1.102.0\bin\code-tunnel.exe tunnel --accept-server-license-terms --log info --name ABO-ELDAHB --parent-process-id 26280

2025-07-12 05:14:55.614 [info] *

2025-07-12 05:14:55.615 [info] * Visual Studio Code Server

2025-07-12 05:14:55.615 [info] *

2025-07-12 05:14:55.616 [info] * By using the software, you agree to

2025-07-12 05:14:55.616 [info] * the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and

2025-07-12 05:14:55.617 [info] * the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).

2025-07-12 05:14:55.617 [info] *

2025-07-12 05:14:55.617 [info] [2025-07-12 05:14:55] warn Command-line options will not be applied until the existing tunnel exits.

2025-07-12 05:14:55.617 [info] [2025-07-12 05:14:55] info 

2025-07-12 05:14:55.617 [info] Open this link in your browser https://vscode.dev/tunnel/abo-eldahb

2025-07-12 05:14:55.618 [info] 

2025-07-12 05:14:55.618 [info] Connected to an existing tunnel process running on this machine.

2025-07-12 05:14:55.618 [info] 

2025-07-12 05:14:55.619 [info] Open this link in your browser https://vscode.dev/tunnel/abo-eldahb

2025-07-12 05:14:55.619 [info] 

