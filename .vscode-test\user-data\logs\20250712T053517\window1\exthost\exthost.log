2025-07-12 05:35:20.758 [info] Extension host with pid 26688 started
2025-07-12 05:35:20.787 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-07-12 05:35:20.878 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-07-12 05:35:20.907 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-07-12 05:35:21.001 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-07-12 05:35:21.193 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-07-12 05:35:21.194 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-07-12 05:35:21.278 [info] Eager extensions activated
2025-07-12 05:35:21.284 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-12 05:35:21.290 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-12 05:35:21.323 [info] Extension host terminating: renderer closed the MessagePort
2025-07-12 05:35:21.338 [info] Extension host with pid 26688 exiting with code 0
