# Build output
out/
dist/
*.vsix

# Dependencies
node_modules/
.vscode-test/

# Source control
.git/
.gitignore

# Development files
.devcontainer/
test/
*.test.js
runTest.js

# Documentation (keep README.md for marketplace)
EXTENSION_STATUS_REPORT.md

# Configuration files
.eslintrc.json
tsconfig.json
webpack.config.js

# Logs
*.log
npm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
