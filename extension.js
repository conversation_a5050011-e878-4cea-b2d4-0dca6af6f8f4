const vscode = require('vscode');
const axios = require('axios');
const {
  TerminalAgent,
  DependenciesAgent,
  MemorySystem,
  WebSearchAgent,
  CodingExpertAgent,
  CoordinatorAgent
} = require('smart-agent-core');

// This will be our main LLM function passed to the coordinator
async function callLLMForPlanning(prompt) {
    const openaiApiKey = await getApiKey('openaiApiKey');
    if (!openaiApiKey) {
        vscode.window.showErrorMessage('❌ OpenAI API Key is required for the Smart Agent to function. Please set it in VS Code settings.');
        throw new Error('OpenAI API Key not found.');
    }
    const defaultModel = vscode.workspace.getConfiguration('smartAgent').get('defaultOpenAIModel') || 'gpt-4o';
    
    const res = await axios.post('https://api.openai.com/v1/chat/completions', {
        model: defaultModel,
        messages: [{ role: 'user', content: prompt }],
    }, {
        headers: { Authorization: `Bear<PERSON> ${openaiApi<PERSON>ey}` }
    });
    return res.data.choices[0].message.content;
}

async function activate(context) {
    console.log('✅ Smart Collaborative Agent with AI active');

    // Instantiate all agents
    const terminalAgent = new TerminalAgent();
    const dependenciesAgent = new DependenciesAgent();
    const memorySystem = new MemorySystem();
    const webSearchAgent = new WebSearchAgent();
    const codingExpertAgent = new CodingExpertAgent();
    
    // Pass the LLM caller to the Coordinator
    const coordinatorAgent = new CoordinatorAgent({
        llm_caller: callLLMForPlanning,
        terminal: terminalAgent,
        dependencies: dependenciesAgent,
        memory: memorySystem,
        webSearch: webSearchAgent,
        codingExpert: codingExpertAgent
    });

    let disposable = vscode.commands.registerCommand('smartAgent.start', async () => {
        vscode.window.showInformationMessage('🤖 الوكيل الذكي جاهز!');
        await showMainMenu(coordinatorAgent);
    });

    context.subscriptions.push(disposable);
}

function deactivate() {
    console.log('🛑 Smart Collaborative Agent deactivated');
}

async function showMainMenu(coordinatorAgent) {
    const choice = await vscode.window.showQuickPick([
        { label: '🚀 تنفيذ مهمة معقدة', description: 'استخدم الوكيل الذكي لتنفيذ مهام متعددة الخطوات' },
        { label: '🤖 استشارة مباشرة للذكاء الاصطناعي', description: 'تحدث مباشرة مع نموذج لغوي' }
    ], {
        placeHolder: 'اختر نوع الخدمة'
    });

    if (!choice) return;

    if (choice.label.includes('تنفيذ مهمة معقدة')) {
        await handleAdvancedTask(coordinatorAgent);
    } else if (choice.label.includes('استشارة مباشرة')) {
        await showAIOptions();
    }
}

async function handleAdvancedTask(coordinatorAgent) {
  const task = await vscode.window.showInputBox({ 
    prompt: '📝 صف المهمة التي تريد من الوكيل الذكي تنفيذها' 
  });

  if (task) {
    vscode.window.withProgress({
      location: vscode.ProgressLocation.Notification,
      title: 'الوكيل الذكي يعمل على مهمتك...',
      cancellable: false
    }, async (progress) => {
      try {
        progress.report({ message: 'يفكر الوكيل في الخطة...' });
        const result = await coordinatorAgent.processTask(task);
        showTaskResult(task, result);
      } catch (error) {
        vscode.window.showErrorMessage(`❌ فشل الوكيل الذكي: ${error.message}`);
      }
    });
  }
}

function showTaskResult(task, result) {
  const panel = vscode.window.createWebviewPanel(
    'taskResult', `نتائج مهمة الوكيل`, vscode.ViewColumn.One, {
    enableScripts: true
  });

  let content = `<h2>نتائج المهمة: ${task}</h2>`;
  content += `<h3>الخطة والنتيجة:</h3><pre>${escapeHtml(result)}</pre>`;

  panel.webview.html = `<!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>نتائج المهمة</title>
      <style>
        body { font-family: sans-serif; padding: 20px; }
        pre { background: #f0f0f0; padding: 10px; border-radius: 5px; white-space: pre-wrap; word-wrap: break-word; }
      </style>
    </head>
    <body>${content}</body>
    </html>`;
}


// --- Direct AI Consultation Functions (from previous version) ---

async function showAIOptions() {
    const choice = await vscode.window.showQuickPick([
        '💡 استخدم OpenAI',
        '🌐 استخدم Gemini (Google)',
        '🦙 استخدم Ollama (محلي)',
    ], {
        placeHolder: 'اختر مزود الذكاء الاصطناعي'
    });

    if (choice) {
        const prompt = await vscode.window.showInputBox({ prompt: '📝 اكتب سؤالك أو وصف الكود' });
        if (prompt) {
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: `جاري معالجة طلبك...`,
                cancellable: false
            }, async (progress) => {
                try {
                    if (choice.includes('OpenAI')) await callOpenAI(prompt, progress);
                    else if (choice.includes('Gemini')) await callGemini(prompt, progress);
                    else if (choice.includes('Ollama')) await callOllama(prompt, progress);
                } catch (error) {
                    console.error("Error during API call:", error);
                    vscode.window.showErrorMessage(`❌ حدث خطأ أثناء استدعاء API: ${error.message}`);
                }
            });
        }
    }
}

async function getApiKey(keyName) {
    return vscode.workspace.getConfiguration('smartAgent').get(keyName);
}

async function callOpenAI(prompt, progress) {
    try {
        const openaiApiKey = await getApiKey('openaiApiKey');
        if (!openaiApiKey) {
            vscode.window.showErrorMessage('❌ OpenAI API Key غير موجود.');
            return;
        }
        const defaultModel = vscode.workspace.getConfiguration('smartAgent').get('defaultOpenAIModel') || 'gpt-4o';
        const maxTokens = vscode.workspace.getConfiguration('smartAgent').get('maxTokens') || 1000;
        const selectedText = getSelectedText();
        const contextPrompt = selectedText ? `\n\nContext:\n${selectedText}` : '';
        progress.report({ message: 'OpenAI: جارٍ إرسال الطلب...' });
        const res = await axios.post('https://api.openai.com/v1/chat/completions', {
            model: defaultModel,
            messages: [{ role: 'user', content: prompt + contextPrompt }],
            max_tokens: maxTokens
        }, {
            headers: { Authorization: `Bearer ${openaiApiKey}` }
        });
        showAIResponse('OpenAI', res.data.choices[0].message.content);
    } catch (error) {
        console.error('OpenAI API Error:', error);
        handleApiError('OpenAI', error);
    }
}

async function callGemini(prompt, progress) {
    try {
        const geminiApiKey = await getApiKey('geminiApiKey');
        if (!geminiApiKey) {
            vscode.window.showErrorMessage('❌ Gemini API Key غير موجود.');
            return;
        }
        const defaultModel = vscode.workspace.getConfiguration('smartAgent').get('defaultGeminiModel') || 'gemini-1.5-pro-latest';
        const selectedText = getSelectedText();
        const contextPrompt = selectedText ? `\n\nContext:\n${selectedText}` : '';
        progress.report({ message: 'Gemini: جارٍ إرسال الطلب...' });
        const res = await axios.post(`https://generativeai.googleapis.com/v1beta/models/${defaultModel}:generateContent?key=${geminiApiKey}`, {
            contents: [{ parts: [{ text: prompt + contextPrompt }] }]
        });
        showAIResponse('Gemini', res.data.candidates[0].content.parts[0].text);
    } catch (error) {
        console.error('Gemini API Error:', error);
        handleApiError('Gemini', error);
    }
}

async function callOllama(prompt, progress) {
    try {
        const defaultModel = vscode.workspace.getConfiguration('smartAgent').get('defaultOllamaModel') || 'llama2';
        const ollamaBaseUrl = vscode.workspace.getConfiguration('smartAgent').get('ollamaUrl') || 'http://localhost:11434';
        const selectedText = getSelectedText();
        const contextPrompt = selectedText ? `\n\nContext:\n${selectedText}` : '';
        progress.report({ message: 'Ollama: جارٍ إرسال الطلب...' });

        // Ollama API endpoint
        const ollamaUrl = `${ollamaBaseUrl}/api/generate`;

        const res = await axios.post(ollamaUrl, {
            model: defaultModel,
            prompt: prompt + contextPrompt,
            stream: false
        });

        showAIResponse('Ollama', res.data.response);
    } catch (error) {
        console.error('Ollama API Error:', error);
        if (error.code === 'ECONNREFUSED') {
            vscode.window.showErrorMessage('❌ لا يمكن الاتصال بـ Ollama. تأكد من تشغيل Ollama على المنفذ 11434');
        } else {
            handleApiError('Ollama', error);
        }
    }
}

function showAIResponse(provider, content) {
    const panel = vscode.window.createWebviewPanel(
        'aiResponse', `${provider} Response`, vscode.ViewColumn.One, {
        enableScripts: true
    });
    panel.webview.html = getWebviewContent(provider, content);
}

function getWebviewContent(provider, content) {
    return `<!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <title>Response</title>
        <style>
            body { font-family: sans-serif; padding: 20px; }
            pre { background: #f0f0f0; padding: 10px; border-radius: 5px; white-space: pre-wrap; word-wrap: break-word; }
            .copy-button { background-color: #007bff; color: white; border: none; padding: 10px 20px; margin-top: 10px; border-radius: 5px; cursor: pointer; }
        </style>
    </head>
    <body>
        <h2>${provider} Response</h2>
        <div>
            <pre>${escapeHtml(content)}</pre>
            <button class="copy-button" onclick="copyToClipboard()">نسخ الرد</button>
        </div>
        <script>
            function copyToClipboard() {
                const text = document.querySelector('pre').innerText;
                navigator.clipboard.writeText(text).then(() => {
                    const btn = document.querySelector('.copy-button');
                    btn.textContent = 'تم النسخ!';
                    setTimeout(() => { btn.textContent = 'نسخ الرد'; }, 2000);
                });
            }
        </script>
    </body>
    </html>`;
}

function escapeHtml(text) {
    if (typeof text !== 'string') {
        return '';
    }
    var map = {
        '&': '&',
        '<': '<',
        '>': '>',
        '"': '"',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

function handleApiError(provider, error) {
    if (error.response) {
        vscode.window.showErrorMessage(`❌ ${provider} API Error: ${error.response.status} - ${error.response.data?.error?.message || JSON.stringify(error.response.data)}`);
    } else {
        vscode.window.showErrorMessage(`❌ ${provider} API Error: ` + error.message);
    }
}

function getSelectedText() {
    const editor = vscode.window.activeTextEditor;
    if (editor) {
        return editor.document.getText(editor.selection);
    }
    return '';
}

module.exports = { activate, deactivate };
