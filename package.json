{"name": "smart-collaborative-agent-ai", "displayName": "Smart Collaborative Agent AI", "description": "وكيل ذكي متكامل مع OpenAI, Gemini, Ollama, OpenRouter, Hugging Face داخل VSCode.", "version": "1.1.0", "publisher": "<PERSON><PERSON><PERSON><PERSON>", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/amramretoo/smart-collaborative-agent-ai"}, "bugs": {"url": "https://github.com/amramretoo/smart-collaborative-agent-ai/issues"}, "homepage": "https://github.com/amramretoo/smart-collaborative-agent-ai#readme", "keywords": ["ai", "artificial-intelligence", "openai", "gemini", "ollama", "chatgpt", "coding-assistant", "vscode-extension", "arabic", "smart-agent"], "categories": ["Other", "Machine Learning", "Education"], "icon": "icon.png", "engines": {"vscode": "^1.80.0"}, "activationEvents": ["onCommand:smartAgent.start"], "main": "./extension.js", "contributes": {"commands": [{"command": "smartAgent.start", "title": "🚀 بدء الوكيل الذكي مع AI"}], "configuration": {"title": "Smart Agent AI", "properties": {"smartAgent.openaiApiKey": {"type": "string", "description": "OpenAI API Key"}, "smartAgent.geminiApiKey": {"type": "string", "description": "Gemini API Key"}, "smartAgent.ollamaApiKey": {"type": "string", "description": "Ollama API Key"}, "smartAgent.openrouterApiKey": {"type": "string", "description": "OpenRouter API Key"}, "smartAgent.huggingfaceApiKey": {"type": "string", "description": "Hugging Face API Key"}, "smartAgent.defaultOpenAIModel": {"type": "string", "description": "OpenAI Default Model", "default": "gpt-4o"}, "smartAgent.defaultGeminiModel": {"type": "string", "description": "Gemini Default Model", "default": "gemini-1.5-pro-latest"}, "smartAgent.defaultOllamaModel": {"type": "string", "description": "<PERSON><PERSON><PERSON> Default Model", "default": "llama2"}, "smartAgent.ollamaUrl": {"type": "string", "description": "Ollama Server URL", "default": "http://localhost:11434"}, "smartAgent.defaultHuggingFaceModel": {"type": "string", "description": "Hugging Face Default Model", "default": "google/flan-t5-xxl"}, "smartAgent.maxTokens": {"type": "number", "description": "Maximum Tokens for Requests", "default": 1000}}}}, "dependencies": {"axios": "^1.6.0", "smart-agent-core": "file:smart-agent-core"}, "devDependencies": {"@types/vscode": "^1.80.0", "vsce": "latest", "mocha": "^10.0.0", "sinon": "^15.0.0", "@vscode/test-electron": "^2.3.0"}, "scripts": {"test": "node ./test/runTest.js"}}