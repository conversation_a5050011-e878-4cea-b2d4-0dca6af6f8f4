# Change Log

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

## [1.1.0] - 2025-07-12

### ✨ إضافات جديدة
- إضافة دعم كامل لـ Ollama (النماذج المحلية)
- إضافة إعدادات مخصصة لـ Ollama URL
- تحسين واجهة المستخدم مع خيارات AI إضافية
- إضافة أيقونة مخصصة للإضافة
- إضافة ملف LICENSE (MIT)
- تحسين ملف .vscodeignore لتقليل حجم الإضافة

### 🔧 تحسينات
- تحسين معالجة الأخطاء لـ Ollama
- إضافة رسائل خطأ واضحة عند عدم توفر Ollama
- تحديث معلومات package.json للنشر
- إضافة كلمات مفتاحية وفئات للإضافة

### 🐛 إصلاحات
- إصلاح مشاكل التوافق مع VS Code
- تحسين استقرار الإضافة

## [1.0.1] - 2025-07-12

### 🔧 تحسينات
- إضافة repository field في package.json
- تحسين معالجة الأخطاء

## [1.0.0] - 2025-07-12

### 🎉 الإصدار الأول
- دعم OpenAI (GPT-4o, GPT-3.5-turbo)
- دعم Google Gemini (1.5 Pro Latest)
- نظام الوكلاء المتخصصين:
  - وكيل التنسيق (CoordinatorAgent)
  - وكيل الطرفية (TerminalAgent)
  - وكيل التبعيات (DependenciesAgent)
  - نظام الذاكرة (MemorySystem)
  - وكيل البحث (WebSearchAgent)
  - وكيل البرمجة (CodingExpertAgent)
- واجهة عربية كاملة
- لوحات ويب تفاعلية لعرض النتائج
- دعم السياق من النص المحدد
- إعدادات مخصصة لكل مزود AI
- اختبارات شاملة (9 اختبارات)

### 🎯 الميزات الأساسية
- تنفيذ مهام معقدة متعددة الخطوات
- استشارة مباشرة للذكاء الاصطناعي
- إدارة مفاتيح API عبر إعدادات VS Code
- نظام ذاكرة دائم بين الجلسات
- معالجة أخطاء متقدمة
- دعم النماذج المخصصة لكل مزود
