# دليل البدء السريع - Smart Collaborative Agent AI

## 🚀 التثبيت والإعداد (5 دقائق)

### 1. تثبيت الإضافة
```bash
# من ملف VSIX
code --install-extension smart-collaborative-agent-ai-1.1.0.vsix

# أو من VS Code Marketplace (قريباً)
```

### 2. إعداد مفاتيح API
```
1. اضغط Ctrl+, (إعدادات VS Code)
2. ابحث عن "Smart Agent AI"
3. أضف مفاتيح API:
```

#### للاستخدام مع Gemini (مجاني):
```json
{
  "smartAgent.geminiApiKey": "AIzaSyBzcsb9sPcg4jzUpsXZYr0CHssPcRDNrLw"
}
```

#### للاستخدام مع Ollama (محلي):
```json
{
  "smartAgent.ollamaUrl": "http://localhost:11434",
  "smartAgent.defaultOllamaModel": "llama2"
}
```

## 🎯 الاستخدام السريع

### الطريقة الأولى: مهام معقدة
```
1. Ctrl+Shift+P
2. اكتب: "🚀 بدء الوكيل الذكي مع AI"
3. اختر: "🚀 تنفيذ مهمة معقدة"
4. اكتب مهمتك: "إنشاء مشروع React جديد"
```

### الطريقة الثانية: استشارة سريعة
```
1. Ctrl+Shift+P
2. اكتب: "🚀 بدء الوكيل الذكي مع AI"
3. اختر: "🤖 استشارة مباشرة للذكاء الاصطناعي"
4. اختر: "🌐 استخدم Gemini" أو "🦙 استخدم Ollama"
5. اكتب سؤالك
```

## 🔧 إعداد Ollama (للاستخدام المحلي)

### تثبيت Ollama:
```bash
# Windows
winget install Ollama.Ollama

# أو تحميل من: https://ollama.ai/download
```

### تشغيل نموذج:
```bash
# تحميل وتشغيل نموذج
ollama pull llama2
ollama serve

# التحقق من النماذج المتاحة
ollama list
```

### النماذج المقترحة:
- `llama2` - نموذج عام جيد (3.8GB)
- `codellama` - متخصص في البرمجة (3.8GB)
- `mistral` - سريع وفعال (4.1GB)
- `phi` - صغير وسريع (1.6GB)

## 📝 أمثلة سريعة

### للبرمجة:
```
"اكتب دالة Python لحساب الأعداد الأولية"
"اشرح هذا الكود" (مع تحديد الكود أولاً)
"أصلح هذا الخطأ في JavaScript"
```

### للمهام المعقدة:
```
"إنشاء API بسيط باستخدام Node.js"
"تحليل ملفات المشروع وإنشاء تقرير"
"البحث عن أحدث مكتبات Python وتثبيتها"
```

## ⚡ نصائح للاستخدام الأمثل

### 1. استخدم السياق:
- حدد الكود قبل طرح السؤال
- سيتم إرفاق الكود تلقائياً كسياق

### 2. اختر المزود المناسب:
- **Gemini**: للأسئلة العامة والتحليل
- **Ollama**: للخصوصية والاستخدام المحلي
- **OpenAI**: للمهام المعقدة (يتطلب مفتاح API مدفوع)

### 3. استخدم الوكلاء المتخصصين:
- للمهام المعقدة، اختر "تنفيذ مهمة معقدة"
- سيقوم النظام بتوزيع المهمة على الوكلاء المناسبين

## 🆘 حل المشاكل السريع

### مشكلة: "Gemini API Key غير موجود"
```
الحل: تأكد من إضافة المفتاح في الإعدادات
```

### مشكلة: "لا يمكن الاتصال بـ Ollama"
```
الحل: 
1. تأكد من تشغيل Ollama: ollama serve
2. تحقق من المنفذ: http://localhost:11434
```

### مشكلة: "الإضافة لا تعمل"
```
الحل:
1. أعد تشغيل VS Code
2. تحقق من تثبيت الإضافة: Extensions > Smart Collaborative Agent AI
```

## 🎉 جاهز للاستخدام!

الآن يمكنك البدء في استخدام الإضافة. جرب الأمثلة أعلاه واستمتع بالبرمجة مع الذكاء الاصطناعي! 🚀
