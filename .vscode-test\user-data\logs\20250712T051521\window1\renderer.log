2025-07-12 05:15:22.677 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-12 05:15:22.679 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-12 05:15:22.680 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-12 05:15:23.383 [info] Started local extension host with pid 20724.
2025-07-12 05:15:23.405 [info] Loading development extension at c:\Users\<USER>\vscode-extion
2025-07-12 05:15:23.881 [info] ChatSessionStore: Migrating 0 chat sessions from storage service to file system
2025-07-12 05:15:24.445 [info] Settings Sync: Account status changed from uninitialized to unavailable
2025-07-12 05:15:26.095 [error] ReferenceError: suite is not defined
	at Object.<anonymous> (c:\Users\<USER>\vscode-extion\test\extension.test.js:5:1)
	at Module._compile (node:internal/modules/cjs/loader:1738:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1904:10)
	at Module.load (node:internal/modules/cjs/loader:1472:32)
	at Module._load (node:internal/modules/cjs/loader:1289:12)
	at c._load (node:electron/js2c/node_init:2:17950)
	at e._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:356:5519)
	at t._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:174:22697)
	at r._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:166:25633)
	at TracingChannel.traceSync (node:diagnostics_channel:322:14)
	at wrapModuleLoad (node:internal/modules/cjs/loader:242:24)
	at Module.require (node:internal/modules/cjs/loader:1494:12)
	at require (node:internal/modules/helpers:135:16)
	at KG.Cb (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:205:1253)
	at KG.yb (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:205:1379)
	at KG.sb (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:117:17435)
	at async KG.$extensionTestsExecute (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:117:17128)
