{"name": "markdown", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "engines": {"vscode": "^1.20.0"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "markdown", "aliases": ["<PERSON><PERSON>", "markdown"], "extensions": [".md", ".mkd", ".mdwn", ".mdown", ".markdown", ".markdn", ".mdtxt", ".mdtext", ".workbook"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "markdown", "scopeName": "text.html.markdown", "path": "./syntaxes/markdown.tmLanguage.json", "embeddedLanguages": {"meta.embedded.block.html": "html", "source.js": "javascript", "source.css": "css", "meta.embedded.block.frontmatter": "yaml", "meta.embedded.block.css": "css", "meta.embedded.block.ini": "ini", "meta.embedded.block.java": "java", "meta.embedded.block.lua": "lua", "meta.embedded.block.makefile": "makefile", "meta.embedded.block.perl": "perl", "meta.embedded.block.r": "r", "meta.embedded.block.ruby": "ruby", "meta.embedded.block.php": "php", "meta.embedded.block.sql": "sql", "meta.embedded.block.vs_net": "vs_net", "meta.embedded.block.xml": "xml", "meta.embedded.block.xsl": "xsl", "meta.embedded.block.yaml": "yaml", "meta.embedded.block.dosbatch": "dosbatch", "meta.embedded.block.clojure": "clojure", "meta.embedded.block.coffee": "coffee", "meta.embedded.block.c": "c", "meta.embedded.block.cpp": "cpp", "meta.embedded.block.diff": "diff", "meta.embedded.block.dockerfile": "dockerfile", "meta.embedded.block.go": "go", "meta.embedded.block.groovy": "groovy", "meta.embedded.block.pug": "jade", "meta.embedded.block.javascript": "javascript", "meta.embedded.block.json": "json", "meta.embedded.block.jsonc": "jsonc", "meta.embedded.block.latex": "latex", "meta.embedded.block.less": "less", "meta.embedded.block.objc": "objc", "meta.embedded.block.scss": "scss", "meta.embedded.block.perl6": "perl6", "meta.embedded.block.powershell": "powershell", "meta.embedded.block.python": "python", "meta.embedded.block.rust": "rust", "meta.embedded.block.scala": "scala", "meta.embedded.block.shellscript": "shellscript", "meta.embedded.block.typescript": "typescript", "meta.embedded.block.typescriptreact": "typescriptreact", "meta.embedded.block.csharp": "csharp", "meta.embedded.block.fsharp": "fsharp"}, "unbalancedBracketScopes": ["markup.underline.link.markdown", "punctuation.definition.list.begin.markdown"]}], "snippets": [{"language": "markdown", "path": "./snippets/markdown.code-snippets"}], "configurationDefaults": {"[markdown]": {"editor.unicodeHighlight.ambiguousCharacters": false, "editor.unicodeHighlight.invisibleCharacters": false, "diffEditor.ignoreTrimWhitespace": false}}}, "scripts": {"update-grammar": "node ../node_modules/vscode-grammar-updater/bin microsoft/vscode-markdown-tm-grammar syntaxes/markdown.tmLanguage ./syntaxes/markdown.tmLanguage.json"}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}