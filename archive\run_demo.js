// This is a demonstration script, not a formal test.
// It mocks the VS Code API to run a simulation of the extension's logic.

const axios = require('axios');
const sinon = require('sinon');

// --- Mock VS Code API ---
const vscodeMock = {
    window: {
        showQuickPick: sinon.stub(),
        showInputBox: sinon.stub(),
        showInformationMessage: (msg) => console.log(`[INFO] ${msg}`),
        showErrorMessage: (msg) => console.error(`[ERROR] ${msg}`),
        createWebviewPanel: sinon.stub(),
        withProgress: async (options, task) => {
            console.log(`[PROGRESS] Starting: ${options.title}`);
            await task({ report: (msg) => console.log(`[PROGRESS] ${msg.message}`) });
            console.log(`[PROGRESS] Finished.`);
        },
    },
    workspace: {
        getConfiguration: () => ({
            get: (key) => {
                if (key === 'geminiApiKey') return process.env.GEMINI_API_KEY || 'fake-gemini-key';
                if (key === 'openaiApiKey') return process.env.OPENAI_API_KEY || 'fake-openai-key';
                return 'default-value';
            }
        }),
    },
    commands: {
        registerCommand: () => {},
        getCommands: () => Promise.resolve(['smartAgent.start']),
        executeCommand: () => {}
    },
    ProgressLocation: {
        Notification: 15
    }
};

// Stub the require call to intercept 'vscode' BEFORE other modules require it
const Module = require('module');
const originalRequire = Module.prototype.require;
Module.prototype.require = function(path) {
  if (path === 'vscode') {
    return vscodeMock;
  }
  // Ensure we are using the correct 'this' context for original require
  return originalRequire.apply(this, arguments);
};

// Now require other modules, including extension.js, which will use the mocked vscode
const { CoordinatorAgent, WebSearchAgent, MemorySystem, TerminalAgent, DependenciesAgent, CodingExpertAgent } = require('./smart-agent-core');
const extension = require('./extension');


// --- Test Simulation ---
async function runTest() {
    console.log('--- STARTING COMPREHENSIVE TEST SIMULATION ---');

    // Mock the webview panel
    const mockPanel = { webview: { html: '' }, onDidDispose: () => {}, dispose: () => {} };
    vscodeMock.window.createWebviewPanel.returns(mockPanel);

    // --- Test Case 1: Direct Gemini Call ---
    console.log('\n--- Testing Direct Gemini Call ---');
    const postStub = sinon.stub(axios, 'post');
    const mockGeminiResponse = {
        data: { candidates: [{ content: { parts: [{ text: 'Gemini says: Hello World!' }] } }] }
    };
    postStub.resolves(mockGeminiResponse);
    
    // Simulate user interaction for Direct Gemini Call
    vscodeMock.window.showQuickPick.onFirstCall().resolves({ label: '🤖 استشارة مباشرة للذكاء الاصطناعي' });
    vscodeMock.window.showQuickPick.onSecondCall().resolves('🌐 استخدم Gemini (Google)');
    vscodeMock.window.showInputBox.resolves('hello');

    const context = { subscriptions: [] };
    await extension.activate(context); // This registers the command
    
    // Simulate the command execution by calling the main menu, which then calls showAIOptions, then callGemini
    await extension.showMainMenu(null); // Pass null for coordinator as it's not used in this path

    if (postStub.called && mockPanel.webview.html.includes('Gemini says: Hello World!')) {
        console.log('✅ Direct Gemini Call Test: SUCCESS');
    } else {
        console.error('❌ Direct Gemini Call Test: FAILED');
        console.log('Post stub called:', postStub.called);
        console.log('Webview HTML:', mockPanel.webview.html);
    }
    postStub.restore();
    sinon.resetHistory(); // Reset stubs for the next test


    // --- Test Case 2: Smart Agent with Web Search ---
    console.log('\n--- Testing Smart Agent (Web Search) ---');
    const llmCallerStub = sinon.stub().resolves(JSON.stringify({
        tool: 'webSearch.deepSearch',
        params: { query: 'test query' }
    }));

    const webSearchStub = sinon.stub(WebSearchAgent.prototype, 'deepSearch').resolves([
        { title: 'Test Result', url: 'https://example.com' }
    ]);

    const coordinator = new CoordinatorAgent({
        llm_caller: llmCallerStub,
        webSearch: new WebSearchAgent(),
        memory: new MemorySystem(),
        terminal: new TerminalAgent(),
        dependencies: new DependenciesAgent(),
        codingExpert: new CodingExpertAgent()
    });

    // Simulate user interaction for Smart Agent
    vscodeMock.window.showQuickPick.resolves({ label: '🚀 تنفيذ مهمة معقدة' });
    vscodeMock.window.showInputBox.resolves('search for something');

    // Manually call the advanced task handler
    await extension.handleAdvancedTask(coordinator);

    // The result of handleAdvancedTask is displayed in a webview, which is mocked.
    // We need to check if the correct action was logged or if the coordinator's processTask was called.
    // The coordinator's processTask returns a summary string.
    // Let's check if the coordinator's processTask was called and if its return value is logged.
    // The 'result' variable in handleAdvancedTask is what's passed to showTaskResult.
    // The coordinator.processTask returns a summary string.
    // We need to check if the coordinator.processTask was called with the correct task.
    // And if the result logged by console.log('Agent Result:', result) is what we expect.

    // The current test structure doesn't directly capture the 'result' from coordinator.processTask.
    // For a more thorough test, we'd need to spy on coordinator.processTask or check its return value.
    // For now, we'll check if the webview was created and if the expected action is in the mocked HTML.
    // The previous assertion `mockPanel.webview.html.includes('Action: webSearch.deepSearch')` is a good check.
    // Let's ensure the correct mocks were used.

    if (llmCallerStub.calledWith(sinon.match.string.includes('search for something')) &&
        webSearchStub.calledOnce &&
        mockPanel.webview.html.includes('Action: webSearch.deepSearch')) {
        console.log('✅ Smart Agent Web Search Test: SUCCESS');
    } else {
        console.error('❌ Smart Agent Web Search Test: FAILED');
        console.log('LLM Caller stub called:', llmCallerStub.called);
        console.log('WebSearch stub called:', webSearchStub.called);
        console.log('Webview HTML:', mockPanel.webview.html);
    }
    webSearchStub.restore();

    console.log('\n--- COMPREHENSIVE TEST SIMULATION FINISHED ---');
}

// The original activate function is not directly called in this simulation.
// We are manually calling the functions we want to test.
// If we wanted to test the command registration and execution flow, we would need to mock `vscode.commands.executeCommand`.

runTest().catch(err => {
    console.error('Test simulation failed with an error:', err);
});
