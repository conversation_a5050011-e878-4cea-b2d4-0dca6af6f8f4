2025-07-12 04:31:31.045 [info] Extension host with pid 4336 started
2025-07-12 04:31:31.137 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-07-12 04:31:31.201 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-07-12 04:31:31.207 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-07-12 04:31:31.257 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-07-12 04:31:31.336 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-07-12 04:31:31.337 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-07-12 04:31:31.390 [info] Eager extensions activated
2025-07-12 04:31:32.551 [warning] [Deprecation Warning] 'PendingMigrationError' is deprecated. navigator is now a global in nodejs, please see https://aka.ms/vscode-extensions/navigator for additional info on this error.
 FROM: PendingMigrationError: navigator is now a global in nodejs, please see https://aka.ms/vscode-extensions/navigator for additional info on this error.
	at get (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:356:6146)
	at withGlobal (c:\Users\<USER>\vscode-extion\node_modules\@sinonjs\fake-timers\src\fake-timers-src.js:137:31)
	at Object.<anonymous> (c:\Users\<USER>\vscode-extion\node_modules\@sinonjs\fake-timers\src\fake-timers-src.js:1782:31)
	at Module._compile (node:internal/modules/cjs/loader:1738:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1904:10)
	at Module.load (node:internal/modules/cjs/loader:1472:32)
	at Module._load (node:internal/modules/cjs/loader:1289:12)
	at c._load (node:electron/js2c/node_init:2:17950)
	at e._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:356:5519)
	at t._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:174:22697)
	at r._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:166:25633)
	at TracingChannel.traceSync (node:diagnostics_channel:322:14)
	at wrapModuleLoad (node:internal/modules/cjs/loader:242:24)
	at Module.require (node:internal/modules/cjs/loader:1494:12)
	at require (node:internal/modules/helpers:135:16)
	at Object.<anonymous> (c:\Users\<USER>\vscode-extion\node_modules\sinon\lib\sinon\util\fake-timers.js:4:20)
	at Module._compile (node:internal/modules/cjs/loader:1738:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1904:10)
	at Module.load (node:internal/modules/cjs/loader:1472:32)
	at Module._load (node:internal/modules/cjs/loader:1289:12)
	at c._load (node:electron/js2c/node_init:2:17950)
	at e._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:356:5519)
	at t._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:174:22697)
	at r._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:166:25633)
	at TracingChannel.traceSync (node:diagnostics_channel:322:14)
	at wrapModuleLoad (node:internal/modules/cjs/loader:242:24)
	at Module.require (node:internal/modules/cjs/loader:1494:12)
	at require (node:internal/modules/helpers:135:16)
	at Object.<anonymous> (c:\Users\<USER>\vscode-extion\node_modules\sinon\lib\sinon\sandbox.js:10:20)
	at Module._compile (node:internal/modules/cjs/loader:1738:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1904:10)
	at Module.load (node:internal/modules/cjs/loader:1472:32)
	at Module._load (node:internal/modules/cjs/loader:1289:12)
	at c._load (node:electron/js2c/node_init:2:17950)
	at e._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:356:5519)
	at t._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:174:22697)
	at r._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:166:25633)
	at TracingChannel.traceSync (node:diagnostics_channel:322:14)
	at wrapModuleLoad (node:internal/modules/cjs/loader:242:24)
	at Module.require (node:internal/modules/cjs/loader:1494:12)
	at require (node:internal/modules/helpers:135:16)
	at Object.<anonymous> (c:\Users\<USER>\vscode-extion\node_modules\sinon\lib\sinon\create-sandbox.js:4:17)
	at Module._compile (node:internal/modules/cjs/loader:1738:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1904:10)
	at Module.load (node:internal/modules/cjs/loader:1472:32)
	at Module._load (node:internal/modules/cjs/loader:1289:12)
	at c._load (node:electron/js2c/node_init:2:17950)
	at e._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:356:5519)
	at t._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:174:22697)
	at r._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:166:25633)
	at TracingChannel.traceSync (node:diagnostics_channel:322:14)
	at wrapModuleLoad (node:internal/modules/cjs/loader:242:24)
	at Module.require (node:internal/modules/cjs/loader:1494:12)
	at require (node:internal/modules/helpers:135:16)
	at Object.<anonymous> (c:\Users\<USER>\vscode-extion\node_modules\sinon\lib\sinon.js:4:23)
	at Module._compile (node:internal/modules/cjs/loader:1738:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1904:10)
	at Module.load (node:internal/modules/cjs/loader:1472:32)
	at Module._load (node:internal/modules/cjs/loader:1289:12)
	at c._load (node:electron/js2c/node_init:2:17950)
	at e._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:356:5519)
	at t._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:174:22697)
	at r._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:166:25633)
	at TracingChannel.traceSync (node:diagnostics_channel:322:14)
	at wrapModuleLoad (node:internal/modules/cjs/loader:242:24)
	at Module.require (node:internal/modules/cjs/loader:1494:12)
	at require (node:internal/modules/helpers:135:16)
	at Object.<anonymous> (c:\Users\<USER>\vscode-extion\test\extension.test.js:4:15)
	at Module._compile (node:internal/modules/cjs/loader:1738:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1904:10)
	at Module.load (node:internal/modules/cjs/loader:1472:32)
	at Module._load (node:internal/modules/cjs/loader:1289:12)
	at c._load (node:electron/js2c/node_init:2:17950)
	at e._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:356:5519)
	at t._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:174:22697)
	at r._load (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:166:25633)
	at TracingChannel.traceSync (node:diagnostics_channel:322:14)
	at wrapModuleLoad (node:internal/modules/cjs/loader:242:24)
	at Module.require (node:internal/modules/cjs/loader:1494:12)
	at require (node:internal/modules/helpers:135:16)
	at KG.Cb (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:205:1253)
	at KG.yb (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:205:1379)
	at KG.sb (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:117:17435)
	at async KG.$extensionTestsExecute (file:///c:/Users/<USER>/vscode-extion/.vscode-test/vscode-win32-x64-archive-1.102.0/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:117:17128)
2025-07-12 04:31:33.058 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-12 04:31:33.062 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-12 04:31:33.094 [info] Extension host terminating: renderer closed the MessagePort
2025-07-12 04:31:33.103 [info] Extension host with pid 4336 exiting with code 0
