const assert = require('assert');
const path = require('path');
const fs = require('fs');

// Integration tests for Smart Agent AI Extension
describe('Smart Agent AI Integration Tests', function() {
    
    it('should have all required files', function() {
        const requiredFiles = [
            'extension.js',
            'package.json',
            'smart-agent-core/index.js',
            'smart-agent-core/coordinator_agent.js',
            'smart-agent-core/agent_terminal.js',
            'smart-agent-core/agent_dependencies.js',
            'smart-agent-core/memory_system.js',
            'smart-agent-core/web_search_agent.js',
            'smart-agent-core/coding_expert_agent.js'
        ];
        
        requiredFiles.forEach(file => {
            const filePath = path.join(__dirname, '..', file);
            assert.ok(fs.existsSync(filePath), `Required file ${file} exists`);
        });
    });
    
    it('should have valid package.json structure', function() {
        const packagePath = path.join(__dirname, '../package.json');
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        
        // Check required fields
        assert.ok(packageJson.name, 'Package has name');
        assert.ok(packageJson.version, 'Package has version');
        assert.ok(packageJson.main === './extension.js', 'Main entry point is correct');
        assert.ok(packageJson.engines && packageJson.engines.vscode, 'VSCode engine specified');
        assert.ok(packageJson.activationEvents, 'Activation events defined');
        assert.ok(packageJson.contributes, 'Contributes section exists');
        assert.ok(packageJson.contributes.commands, 'Commands defined');
        assert.ok(packageJson.contributes.configuration, 'Configuration defined');
    });
    
    it('should load smart-agent-core modules', function() {
        const smartAgentCore = require('../smart-agent-core');
        
        // Check all required exports
        assert.ok(smartAgentCore.CoordinatorAgent, 'CoordinatorAgent exported');
        assert.ok(smartAgentCore.TerminalAgent, 'TerminalAgent exported');
        assert.ok(smartAgentCore.DependenciesAgent, 'DependenciesAgent exported');
        assert.ok(smartAgentCore.MemorySystem, 'MemorySystem exported');
        assert.ok(smartAgentCore.WebSearchAgent, 'WebSearchAgent exported');
        assert.ok(smartAgentCore.CodingExpertAgent, 'CodingExpertAgent exported');
    });
    
    it('should instantiate agents correctly', function() {
        const {
            TerminalAgent,
            DependenciesAgent,
            MemorySystem,
            WebSearchAgent,
            CodingExpertAgent,
            CoordinatorAgent
        } = require('../smart-agent-core');
        
        // Test agent instantiation
        const terminalAgent = new TerminalAgent();
        const dependenciesAgent = new DependenciesAgent();
        const memorySystem = new MemorySystem();
        const webSearchAgent = new WebSearchAgent();
        const codingExpertAgent = new CodingExpertAgent();
        
        assert.ok(terminalAgent, 'TerminalAgent instantiated');
        assert.ok(dependenciesAgent, 'DependenciesAgent instantiated');
        assert.ok(memorySystem, 'MemorySystem instantiated');
        assert.ok(webSearchAgent, 'WebSearchAgent instantiated');
        assert.ok(codingExpertAgent, 'CodingExpertAgent instantiated');
        
        // Test coordinator with mock LLM
        const mockLLM = async (prompt) => '{"tool": "final_answer", "params": {"answer": "Test response"}}';
        
        const coordinatorAgent = new CoordinatorAgent({
            llm_caller: mockLLM,
            terminal: terminalAgent,
            dependencies: dependenciesAgent,
            memory: memorySystem,
            webSearch: webSearchAgent,
            codingExpert: codingExpertAgent
        });
        
        assert.ok(coordinatorAgent, 'CoordinatorAgent instantiated');
    });
    
    it('should have proper configuration schema', function() {
        const packagePath = path.join(__dirname, '../package.json');
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        const config = packageJson.contributes.configuration;
        
        // Check required configuration properties
        const requiredProps = [
            'smartAgent.openaiApiKey',
            'smartAgent.geminiApiKey',
            'smartAgent.defaultOpenAIModel',
            'smartAgent.defaultGeminiModel',
            'smartAgent.maxTokens'
        ];
        
        requiredProps.forEach(prop => {
            assert.ok(config.properties[prop], `Configuration property ${prop} exists`);
        });
    });
    
    it('should have valid command registration', function() {
        const packagePath = path.join(__dirname, '../package.json');
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        const commands = packageJson.contributes.commands;
        
        assert.ok(Array.isArray(commands), 'Commands is an array');
        assert.ok(commands.length > 0, 'At least one command defined');
        
        const startCommand = commands.find(cmd => cmd.command === 'smartAgent.start');
        assert.ok(startCommand, 'Start command exists');
        assert.ok(startCommand.title, 'Start command has title');
    });
});
