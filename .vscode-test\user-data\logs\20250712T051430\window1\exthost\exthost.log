2025-07-12 05:14:34.282 [info] Extension host with pid 3608 started
2025-07-12 05:14:34.321 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-07-12 05:14:34.438 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-07-12 05:14:34.451 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-07-12 05:14:34.545 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-07-12 05:14:34.682 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-07-12 05:14:34.683 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-07-12 05:14:34.809 [info] Eager extensions activated
2025-07-12 05:14:34.818 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-12 05:14:34.826 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-12 05:14:34.874 [info] Extension host terminating: renderer closed the MessagePort
2025-07-12 05:14:34.897 [info] Extension host with pid 3608 exiting with code 0
