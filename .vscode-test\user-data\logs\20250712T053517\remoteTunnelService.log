2025-07-12 05:35:20.536 [info] Other tunnel running, attaching...
2025-07-12 05:35:20.536 [info] Running tunnel CLI

2025-07-12 05:35:20.536 [info] tunnel Spawning: c:\Users\<USER>\vscode-extion\.vscode-test\vscode-win32-x64-archive-1.102.0\bin\code-tunnel.exe tunnel --accept-server-license-terms --log info --name ABO-ELDAHB --parent-process-id 24224

2025-07-12 05:35:20.584 [info] *

2025-07-12 05:35:20.584 [info] * Visual Studio Code Server

2025-07-12 05:35:20.585 [info] *

2025-07-12 05:35:20.585 [info] * By using the software, you agree to

2025-07-12 05:35:20.585 [info] * the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and

2025-07-12 05:35:20.586 [info] * the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).

2025-07-12 05:35:20.586 [info] *

2025-07-12 05:35:20.586 [info] [2025-07-12 05:35:20] warn Command-line options will not be applied until the existing tunnel exits.

2025-07-12 05:35:20.586 [info] Connected to an existing tunnel process running on this machine.

2025-07-12 05:35:20.587 [info] 

2025-07-12 05:35:20.587 [info] Open this link in your browser https://vscode.dev/tunnel/abo-eldahb

2025-07-12 05:35:20.587 [info] 

