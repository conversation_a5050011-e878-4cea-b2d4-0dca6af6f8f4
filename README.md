# Smart Collaborative Agent AI 🤖

إضافة VSCode ذكية ومتطورة توفر وكيل تعاوني متكامل مع أحدث منصات الذكاء الاصطناعي، مع نظام وكلاء متخصصين وذاكرة دائمة لتحسين تجربة البرمجة والتطوير.

## ✨ الميزات الرئيسية

### 🎯 نظام الوكلاء المتخصصين
- **🖥️ وكيل الطرفية (TerminalAgent)**: يدير تنفيذ الأوامر الطرفية ويتتبع التاريخ
- **📦 وكيل التبعيات (DependenciesAgent)**: يدير تثبيت وإزالة الحزم والمكتبات
- **🧠 نظام الذاكرة (MemorySystem)**: يخزن البيانات بين الجلسات في ملف `memory.json`
- **🔍 وكيل البحث (WebSearchAgent)**: يبحث في الويب عن المعلومات المطلوبة
- **💻 وكيل البرمجة (CodingExpertAgent)**: يولد ويحلل الكود البرمجي
- **🎛️ وكيل التنسيق (CoordinatorAgent)**: ينسق بين جميع الوكلاء لتنفيذ المهام المعقدة

### 🌐 تكامل شامل مع منصات الذكاء الاصطناعي
- **OpenAI** (GPT-4o, GPT-3.5-turbo)
- **Google Gemini** (1.5 Pro Latest)
- **Ollama** (النماذج المحلية)
- **OpenRouter** (وصول لنماذج متعددة)
- **Hugging Face** (نماذج مفتوحة المصدر)

### 🎨 واجهة مستخدم متقدمة
- واجهة عربية كاملة وسهلة الاستخدام
- قوائم سريعة لاختيار مزود الذكاء الاصطناعي
- لوحات ويب تفاعلية لعرض النتائج
- إمكانية نسخ النتائج بنقرة واحدة
- دعم السياق من النص المحدد في المحرر

## 📁 هيكل المشروع

### 🎯 **الملفات الأساسية (للاستخدام):**
```
smart-collaborative-agent-ai/
├── 📦 smart-collaborative-agent-ai-1.1.0.vsix  # الإضافة النهائية للتثبيت ⭐
├── 📄 extension.js                             # الملف الرئيسي للإضافة
├── 📄 package.json                             # إعدادات المشروع والتبعيات
├── 📄 LICENSE                                  # رخصة المشروع (MIT)
├── 🎨 icon.png                                 # أيقونة الإضافة
├── 📚 README.md                                # دليل المشروع (هذا الملف)
├── 📚 CHANGELOG.md                             # سجل التغييرات
├── 📚 QUICK_START.md                           # دليل البدء السريع
├── 📁 smart-agent-core/                        # النظام الأساسي للوكلاء
│   ├── 📄 index.js                            # نقطة تصدير الوحدات
│   ├── 📄 coordinator_agent.js                # وكيل التنسيق الرئيسي
│   ├── 📄 agent_terminal.js                   # وكيل إدارة الطرفية
│   ├── 📄 agent_dependencies.js               # وكيل إدارة التبعيات
│   ├── 📄 memory_system.js                    # نظام الذاكرة الدائمة
│   ├── 📄 web_search_agent.js                 # وكيل البحث على الويب
│   └── 📄 coding_expert_agent.js              # وكيل خبير البرمجة
└── 📁 archive/                                # ملفات مؤرشفة (غير مستخدمة)
    ├── 📁 test/                               # اختبارات سابقة
    ├── 📄 EXTENSION_STATUS_REPORT.md          # تقارير قديمة
    └── 📦 إصدارات سابقة من الإضافة
```

## 🚀 طرق التشغيل والتثبيت

### الطريقة الأولى: التثبيت من ملف VSIX (الأسرع) ⚡
```bash
# 1. تحميل ملف الإضافة
# تأكد من وجود ملف smart-collaborative-agent-ai-1.1.0.vsix

# 2. تثبيت الإضافة في VS Code
code --install-extension smart-collaborative-agent-ai-1.1.0.vsix

# 3. إعادة تشغيل VS Code
# File > Reload Window أو Ctrl+Shift+P > Developer: Reload Window
```

### الطريقة الثانية: التثبيت من المصدر (للمطورين) 🛠️
```bash
# 1. استنساخ أو تحميل المشروع
git clone <repository-url>
cd vscode-extion

# 2. تثبيت التبعيات
npm install

# 3. تجميع الإضافة
npx vsce package

# 4. تثبيت الإضافة المجمعة
code --install-extension smart-collaborative-agent-ai-1.1.0.vsix
```

### الطريقة الثالثة: التشغيل في وضع التطوير 🔧
```bash
# 1. فتح المشروع في VS Code
code .

# 2. الضغط على F5 أو
# Run > Start Debugging

# 3. سيفتح نافذة VS Code جديدة مع الإضافة محملة للاختبار
```

## ⚙️ إعداد مفاتيح API

### الخطوة 1: فتح إعدادات VS Code
```
File > Preferences > Settings
أو
Ctrl + , (Windows/Linux)
Cmd + , (Mac)
```

### الخطوة 2: البحث عن إعدادات الإضافة
```
ابحث عن: Smart Agent AI
أو انتقل إلى: Extensions > Smart Agent AI
```

### الخطوة 3: إضافة مفاتيح API
```json
{
  "smartAgent.openaiApiKey": "sk-your-openai-key-here",
  "smartAgent.geminiApiKey": "your-gemini-key-here",
  "smartAgent.ollamaApiKey": "your-ollama-key-here",
  "smartAgent.openrouterApiKey": "your-openrouter-key-here",
  "smartAgent.huggingfaceApiKey": "your-huggingface-key-here"
}
```

### الخطوة 4: تخصيص النماذج الافتراضية (اختياري)
```json
{
  "smartAgent.defaultOpenAIModel": "gpt-4o",
  "smartAgent.defaultGeminiModel": "gemini-1.5-pro-latest",
  "smartAgent.defaultOllamaModel": "llama2",
  "smartAgent.defaultHuggingFaceModel": "google/flan-t5-xxl",
  "smartAgent.maxTokens": 1000
}
```

## 📖 كيفية الاستخدام التفصيلي

### 🎯 الطريقة الأولى: تنفيذ مهمة معقدة (نظام الوكلاء)

#### الخطوة 1: تشغيل الإضافة
```
1. اضغط Ctrl+Shift+P (أو Cmd+Shift+P على Mac)
2. اكتب: "🚀 بدء الوكيل الذكي مع AI"
3. اضغط Enter
```

#### الخطوة 2: اختيار نوع الخدمة
```
اختر: "🚀 تنفيذ مهمة معقدة"
```

#### الخطوة 3: وصف المهمة
```
أمثلة على المهام المعقدة:
- "إنشاء مشروع React جديد مع TypeScript"
- "البحث عن أحدث مكتبات Python للذكاء الاصطناعي وتثبيتها"
- "تحليل الكود في المجلد الحالي وإنشاء تقرير"
- "إنشاء API بسيط باستخدام Node.js و Express"
```

#### الخطوة 4: مراقبة التقدم
```
- سيظهر شريط تقدم يوضح حالة المهمة
- سيتم عرض النتائج في لوحة ويب تفاعلية
- يمكنك نسخ النتائج أو الكود المولد
```

### 💬 الطريقة الثانية: استشارة مباشرة للذكاء الاصطناعي

#### الخطوة 1: تشغيل الإضافة
```
Ctrl+Shift+P > "🚀 بدء الوكيل الذكي مع AI"
```

#### الخطوة 2: اختيار نوع الخدمة
```
اختر: "🤖 استشارة مباشرة للذكاء الاصطناعي"
```

#### الخطوة 3: اختيار مزود AI
```
- 💡 استخدم OpenAI (GPT-4o)
- 🌐 استخدم Gemini (Google)
```

#### الخطوة 4: طرح السؤال
```
أمثلة على الأسئلة:
- "اشرح لي مفهوم async/await في JavaScript"
- "كيف أحسن أداء قاعدة البيانات؟"
- "ما هي أفضل الممارسات في React؟"
- "اكتب دالة Python لحساب الأعداد الأولية"
```

### 🎨 استخدام السياق من المحرر
```
1. حدد أي نص في محرر VS Code
2. شغل الإضافة واختر "استشارة مباشرة"
3. اكتب سؤالك - سيتم إرفاق النص المحدد تلقائياً كسياق
4. احصل على إجابة مخصصة بناءً على الكود المحدد
```

## 🔧 الاختبار والتطوير

### تشغيل الاختبارات
```bash
# تشغيل جميع الاختبارات
npm test

# تشغيل اختبارات محددة
npx mocha test/extension.test.js
npx mocha test/integration.test.js

# تشغيل اختبارات VS Code المتكاملة
node test/runTest.js
```

### بناء الإضافة للتوزيع
```bash
# تجميع الإضافة
npx vsce package

# تجميع مع معلومات إضافية
npx vsce package --out ./dist/

# فحص الإضافة قبل النشر
npx vsce ls
```

## 🛠️ استكشاف الأخطاء وحلها

### مشاكل شائعة وحلولها

#### ❌ "OpenAI API Key غير موجود"
```
الحل:
1. تأكد من إضافة مفتاح API في الإعدادات
2. تحقق من صحة المفتاح
3. أعد تشغيل VS Code
```

#### ❌ "فشل في تحميل الإضافة"
```
الحل:
1. تأكد من تثبيت Node.js (الإصدار 16 أو أحدث)
2. شغل: npm install
3. أعد تجميع الإضافة: npx vsce package
4. أعد التثبيت
```

#### ❌ "الاختبارات لا تعمل"
```
الحل:
1. تأكد من تثبيت التبعيات: npm install
2. تحقق من إصدار VS Code (1.80.0 أو أحدث)
3. شغل: npm test
```

#### ❌ "بطء في الاستجابة"
```
الحل:
1. تحقق من اتصال الإنترنت
2. جرب نموذج AI مختلف
3. قلل عدد الرموز المميزة (maxTokens)
```

### تسجيل الأخطاء
```
1. افتح Developer Console: Help > Toggle Developer Tools
2. تحقق من تبويب Console للأخطاء
3. ابحث عن رسائل تبدأ بـ "Smart Agent"
```

## 📋 متطلبات النظام

### الحد الأدنى
- **VS Code**: الإصدار 1.80.0 أو أحدث
- **Node.js**: الإصدار 16.0.0 أو أحدث
- **نظام التشغيل**: Windows 10, macOS 10.15, Ubuntu 18.04 أو أحدث
- **الذاكرة**: 4 GB RAM
- **مساحة القرص**: 100 MB

### الموصى به
- **VS Code**: أحدث إصدار
- **Node.js**: الإصدار 18.0.0 أو أحدث
- **الذاكرة**: 8 GB RAM أو أكثر
- **اتصال إنترنت**: سريع ومستقر

## 🤝 المساهمة في المشروع

### كيفية المساهمة
```bash
# 1. استنساخ المشروع
git clone <repository-url>
cd smart-collaborative-agent-ai

# 2. إنشاء فرع جديد
git checkout -b feature/new-feature

# 3. إجراء التغييرات
# ... قم بالتعديلات المطلوبة

# 4. تشغيل الاختبارات
npm test

# 5. إرسال التغييرات
git add .
git commit -m "إضافة ميزة جديدة"
git push origin feature/new-feature

# 6. إنشاء Pull Request
```

### إرشادات المساهمة
- اتبع نمط الكود الموجود
- أضف اختبارات للميزات الجديدة
- حدث الوثائق عند الحاجة
- استخدم رسائل commit واضحة

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم والتواصل

- **الإبلاغ عن الأخطاء**: [GitHub Issues](https://github.com/username/smart-collaborative-agent-ai/issues)
- **طلب ميزات جديدة**: [GitHub Discussions](https://github.com/username/smart-collaborative-agent-ai/discussions)
- **الوثائق**: [Wiki](https://github.com/username/smart-collaborative-agent-ai/wiki)

## 🎉 شكر خاص

شكراً لجميع المساهمين والمطورين الذين ساعدوا في تطوير هذه الإضافة.

---

**استمتع بالبرمجة مع الذكاء الاصطناعي! 🚀✨**
