2025-07-12 05:15:25.331 [info] Extension host with pid 20724 started
2025-07-12 05:15:25.414 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-07-12 05:15:25.681 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-07-12 05:15:25.716 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-07-12 05:15:25.838 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-07-12 05:15:25.998 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-07-12 05:15:25.999 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-07-12 05:15:26.086 [info] Eager extensions activated
2025-07-12 05:15:26.095 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-07-12 05:15:26.104 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-07-12 05:15:26.152 [info] Extension host terminating: renderer closed the MessagePort
2025-07-12 05:15:26.174 [info] Extension host with pid 20724 exiting with code 0
