<?xml version="1.0" encoding="utf-8"?>
<policyDefinitionResources revision="1.0" schemaVersion="1.0">
	<displayName />
	<description />
	<resources>
		<stringTable>
			<string id="Application">Visual Studio Code</string>
			<string id="Supported_1_101">Visual Studio Code &gt;= 1.101</string>
			<string id="Supported_1_67">Visual Studio Code &gt;= 1.67</string>
			<string id="Supported_1_96">Visual Studio Code &gt;= 1.96</string>
			<string id="Supported_1_99">Visual Studio Code &gt;= 1.99</string>
			<string id="Category_extensionsConfigurationTitle">延伸模組</string>
			<string id="Category_interactiveSessionConfigurationTitle">聊天</string>
			<string id="Category_updateConfigurationTitle">更新</string>
			<string id="Category_telemetryConfigurationTitle">遙測</string>
			<string id="ExtensionGalleryServiceUrl">ExtensionGalleryServiceUrl</string>
			<string id="ExtensionGalleryServiceUrl_extensions_gallery_serviceUrl">設定要連線至的 Marketplace 服務 URL</string>
			<string id="ChatToolsAutoApprove">ChatToolsAutoApprove</string>
			<string id="ChatToolsAutoApprove_chat_tools_autoApprove_description">Controls whether tool use should be automatically approved. Allow all tools to run automatically without user confirmation, overriding any tool-specific settings such as terminal auto-approval. Use with caution: carefully review selected tools and be extra wary of possible sources of prompt injection!</string>
			<string id="ChatMCP">ChatMCP</string>
			<string id="ChatMCP_chat_mcp_enabled">啟用與模型內容通訊協定伺服器的整合，以提供其他工具和功能。</string>
			<string id="ChatAgentExtensionTools">ChatAgentExtensionTools</string>
			<string id="ChatAgentExtensionTools_chat_extensionToolsPolicy">啟用使用第三方延伸模組所提供的工具。</string>
			<string id="ChatAgentMode">ChatAgentMode</string>
			<string id="ChatAgentMode_chat_agent_enabled_description">為 {0} 啟用代理程式模式。啟用此功能時，可透過檢視中的下拉式清單啟用代理程式模式。</string>
			<string id="McpGalleryServiceUrl">McpGalleryServiceUrl</string>
			<string id="McpGalleryServiceUrl_mcp_gallery_serviceUrl">設定要連線的 MCP 圖庫服務 URL</string>
			<string id="ChatPromptFiles">ChatPromptFiles</string>
			<string id="ChatPromptFiles_chat_promptFiles_policy">在 [聊天]、[編輯] 及 [即時聊天] 工作階段中啟用可重複使用的提示和指令檔案。</string>
			<string id="UpdateMode">UpdateMode</string>
			<string id="UpdateMode_updateMode">設定是否要接收自動更新。變更後需要重新啟動。系統會從 Microsoft 線上服務擷取更新。</string>
			<string id="UpdateMode_none">停用更新。</string>
			<string id="UpdateMode_manual">停用自動背景更新檢查。若您手動檢查更新，可取得更新。</string>
			<string id="UpdateMode_start">僅在啟動時檢查更新。停用自動背景更新檢查。</string>
			<string id="UpdateMode_default">啟用自動更新檢查。程式碼會自動並定期檢查更新。</string>
			<string id="TelemetryLevel">TelemetryLevel</string>
			<string id="TelemetryLevel_telemetry_telemetryLevel_policyDescription">控制遙測層級。</string>
			<string id="EnableFeedback">EnableFeedback</string>
			<string id="EnableFeedback_telemetry_feedback_enabled">在 Copilot Chat 等功能中啟用意見反應機制，例如問題回報程式、調查和意見反應選項。</string>
			<string id="AllowedExtensions">AllowedExtensions</string>
			<string id="AllowedExtensions_extensions_allowed_policy">指定允許使用的延伸模組清單。這可透過限制使用未經授權的延伸模組，協助保有安全且一致的開發環境。詳細資訊: https://code.visualstudio.com/docs/setup/enterprise#_configure-allowed-extensions</string>
		</stringTable>
		<presentationTable>
			<presentation id="ExtensionGalleryServiceUrl"><textBox refId="ExtensionGalleryServiceUrl"><label>ExtensionGalleryServiceUrl:</label></textBox></presentation>
			<presentation id="ChatToolsAutoApprove"><checkBox refId="ChatToolsAutoApprove">ChatToolsAutoApprove</checkBox></presentation>
			<presentation id="ChatMCP"><checkBox refId="ChatMCP">ChatMCP</checkBox></presentation>
			<presentation id="ChatAgentExtensionTools"><checkBox refId="ChatAgentExtensionTools">ChatAgentExtensionTools</checkBox></presentation>
			<presentation id="ChatAgentMode"><checkBox refId="ChatAgentMode">ChatAgentMode</checkBox></presentation>
			<presentation id="McpGalleryServiceUrl"><textBox refId="McpGalleryServiceUrl"><label>McpGalleryServiceUrl:</label></textBox></presentation>
			<presentation id="ChatPromptFiles"><checkBox refId="ChatPromptFiles">ChatPromptFiles</checkBox></presentation>
			<presentation id="UpdateMode"><dropdownList refId="UpdateMode" /></presentation>
			<presentation id="TelemetryLevel"><textBox refId="TelemetryLevel"><label>TelemetryLevel:</label></textBox></presentation>
			<presentation id="EnableFeedback"><checkBox refId="EnableFeedback">EnableFeedback</checkBox></presentation>
			<presentation id="AllowedExtensions"><multiTextBox refId="AllowedExtensions" /></presentation>
		</presentationTable>
	</resources>
</policyDefinitionResources>
