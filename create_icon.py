from PIL import Image, ImageDraw, ImageFont
import os

# Create a 128x128 icon for the VS Code extension
def create_icon():
    # Create a new image with a gradient background
    size = 128
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Create a gradient background (blue to purple)
    for y in range(size):
        r = int(64 + (y / size) * 128)
        g = int(128 + (y / size) * 64)
        b = int(255 - (y / size) * 64)
        draw.line([(0, y), (size, y)], fill=(r, g, b, 255))
    
    # Draw a robot/AI symbol
    # Head circle
    head_size = 40
    head_x = size // 2 - head_size // 2
    head_y = 20
    draw.ellipse([head_x, head_y, head_x + head_size, head_y + head_size], 
                 fill=(255, 255, 255, 255), outline=(0, 0, 0, 255), width=2)
    
    # Eyes
    eye_size = 8
    left_eye_x = head_x + 10
    right_eye_x = head_x + head_size - 18
    eye_y = head_y + 12
    draw.ellipse([left_eye_x, eye_y, left_eye_x + eye_size, eye_y + eye_size], 
                 fill=(0, 100, 255, 255))
    draw.ellipse([right_eye_x, eye_y, right_eye_x + eye_size, eye_y + eye_size], 
                 fill=(0, 100, 255, 255))
    
    # Body rectangle
    body_width = 50
    body_height = 40
    body_x = size // 2 - body_width // 2
    body_y = head_y + head_size + 5
    draw.rectangle([body_x, body_y, body_x + body_width, body_y + body_height], 
                   fill=(255, 255, 255, 255), outline=(0, 0, 0, 255), width=2)
    
    # Add "AI" text
    try:
        # Try to use a font, fallback to default if not available
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    text = "AI"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    text_x = size // 2 - text_width // 2
    text_y = body_y + body_height + 10
    draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)
    
    # Save the icon
    img.save('icon.png', 'PNG')
    print("Icon created successfully: icon.png")

if __name__ == "__main__":
    create_icon()
